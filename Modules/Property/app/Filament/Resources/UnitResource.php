<?php

namespace Modules\Property\app\Filament\Resources;

use Filament\Tables;
use Filament\Forms\Get;
use Filament\Forms\Form;
use Filament\Tables\Table;
use App\Enums\PropertyTypeEnum;
use Filament\Resources\Resource;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Toggle;
use Modules\Property\app\Models\Unit;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\TextInput;
use Filament\Notifications\Notification;
use Filament\Tables\Actions\ActionGroup;
use Filament\Forms\Components\DatePicker;
use Illuminate\Database\Eloquent\Builder;
use Modules\Property\app\Models\Property;
use Modules\Property\app\Models\Attribute;
use Modules\Property\app\Models\Usability;

use Modules\Property\Enums\PropertyStatus;
use Filament\Forms\Components\Actions\Action;
use Modules\Property\app\Models\PropertyType;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Filament\Forms\Components\SpatieMediaLibraryFileUpload;
use Tapp\FilamentValueRangeFilter\Filters\ValueRangeFilter;
use Modules\Property\app\Filament\Resources\UnitResource\Widgets\UnitListOverview;
use Modules\Property\Enums\UnitPeriodType;

class UnitResource extends Resource
{
    protected static ?string $model = Unit::class;

    protected static ?string $navigationIcon = 'heroicon-o-home';

    protected static ?string $navigationGroup = 'Properties Management';

    protected const RADIO_DECK_COLOR = '#0f2c24';



    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Grid::make(3)
                    ->schema([
                        Hidden::make('company_id')
                            ->default(fn ($livewire) => $livewire->getOwnerRecord()->company_id),
                        TextInput::make('number')
                            ->label(__('Unit Number'))
                            ->placeholder(__('Unit Number'))
                            ->required(),
                        Select::make('property_type_id')
                            ->label(__('Unit Type'))
                            ->required()
                            ->relationship(
                                name: 'property_type',
                                titleAttribute: 'name',
                                modifyQueryUsing: fn($query) => $query->where('property_type', PropertyTypeEnum::Unit->value)
                            )
                            ->searchable(['name'])
                            ->preload()
                            ->live()
                            ->afterStateUpdated(function ($state, callable $set, $get) {
                                $attributes = $get('attributes') ?? [];
                                foreach ($attributes as $key => $attribute) {
                                    $set("attributes.{$key}.attribute_id", null);
                                }
                            }),

                        Select::make('usability_id')
                            ->relationship(name: 'usability', titleAttribute: 'name')
                            ->label(__('Usability'))
                            ->searchable(['name'])
                            ->preload()
                            ->required()
                            ->createOptionForm(fn(Form $form) => UsabilityResource::form($form))
                            ->createOptionAction(
                                fn(Action $action) => $action->visible(fn() => auth()->user()->can('create', Usability::class))
                                    ->modalWidth('2xl')
                            ),

                        DatePicker::make('building_date')
                            ->label(__('Building Date'))
                            ->placeholder(__('Building Date'))
                            ->minDate(fn($record) => $record->property->building_date)
                            ->default(function ($record) {
                                return $record->property->building_date;
                            })
                            ->native(false),
                        TextInput::make('price')
                            ->label(__('Price'))
                            ->placeholder(__('Price'))
                            ->required()
                            ->numeric() // Ensures that the input is treated as a number
                            ->rules([
                                'numeric',
                                'decimal:0,2', // Allows up to 2 decimal places
                                'max:99999999.99', // Maximum value for decimal(10,2)
                                'min:0',
                                'regex:/^\d{1,8}(\.\d{0,2})?$/' // Ensures max 8 digits before decimal and 2 after
                            ])
                            ->placeholder(__('Enter unit price'))
                            ->helperText(__('Price must be a decimal value (e.g., 1000.50)')),
                        Repeater::make('pricePlans')
                            ->relationship('prices')
                            ->reorderable(false)
                            ->label(__('Price Plans'))
                            ->columnSpanFull()
                            ->defaultItems(0)
                            ->collapsed(false)
                            ->columns(2)
                            ->grid(2)
                            ->maxItems(3)
                            ->collapsible(true)
                            ->itemLabel(fn (array $state): ?string => UnitPeriodType::trans($state['period_type'] ?? null))
                            ->schema([
                                Select::make('period_type')
                                    ->label(__('Period Type'))
                                    ->options(fn() => UnitPeriodType::repeatedLabels())
                                    ->native(false)
                                    ->required()
                                    ->live()
                                    ->disableOptionsWhenSelectedInSiblingRepeaterItems()
                                    ->afterStateUpdated(function($state,$get,$set){
                                        $annualPrice = $get('../../price');
                                        if($state == UnitPeriodType::MONTHLY){
                                            $price = $annualPrice / 12;
                                            $set('price', number_format($price, 2, '.', ''));
                                        }elseif($state == UnitPeriodType::HALF_ANNUALLY){
                                            $price = $annualPrice / 2;
                                            $set('price', number_format($price, 2, '.', ''));
                                        }elseif($state == UnitPeriodType::QUARTERLY){
                                            $price = $annualPrice / 4;
                                            $set('price', number_format($price, 2, '.', ''));
                                        }
                                        $set('annual_price', $annualPrice);
                                    }),
                                
                                TextInput::make('price')
                                    ->label(__('Price'))
                                    ->placeholder(__('Enter price'))
                                    ->numeric()
                                    ->required()
                                    ->live(true)
                                    ->debounce(700)
                                    ->afterStateUpdated(function ($state, callable $set, callable $get) {
                                        $period = $get('period_type');
                                        if($period == UnitPeriodType::MONTHLY){
                                            $annualPrice = $state * 12;
                                        }elseif($period == UnitPeriodType::HALF_ANNUALLY){
                                            $annualPrice = $state * 2;
                                        }elseif($period == UnitPeriodType::QUARTERLY){
                                            $annualPrice = $state * 4;
                                        }
                                        $set('annual_price', $annualPrice);
                                    })
                                    ->rules([
                                        'numeric',
                                        'decimal:0,2',
                                        'min:0',
                                    ])
                                    ->helperText(function ($get){
                                        $period = $get('period_type');
                                        $basePrice = $get('../../price');
                                        $annualPrice = $get('annual_price');
                                        $diff = $annualPrice - $basePrice;
                                        if($annualPrice){
                                            if($period == UnitPeriodType::MONTHLY){
                                                return __('the difference between the Monthly price and Annually price is'). ' ' .$diff;
                                            }elseif($period == UnitPeriodType::HALF_ANNUALLY){
                                                return __('the difference between the Half Annually price and Annually price is'). ' ' .$diff;
                                            }elseif($period == UnitPeriodType::QUARTERLY){
                                                return __('the difference between the Quarterly price and Annually price is').' '.$diff;
                                            }
                                        }
                                    })
                                    ->validationMessages([
                                        'numeric' => __('Static price must be a number'),
                                        'min' => __('Static price must be greater than or equal to 0'),
                                    ]),
                                Hidden::make('annual_price')
                                    
                            ]),
                    ]),

                Grid::make(2)
                    ->schema([
                        Repeater::make('attributes')
                            ->relationship('attributes')
                            ->label(__('attributes'))
                            ->collapsed(false)
                            ->collapsible()
                            ->schema([
                                Grid::make(2)
                                    ->schema([
                                        Select::make('attribute_id')
                                            ->options(function (Get $get, $livewire) {
                                                // $propertyTypeId = $livewire->data['property_type_id'] ?? null;
                                                $propertyTypeId = $get('../../property_type_id');
                                                if (!$propertyTypeId) {
                                                    return [];
                                                }
                                                return Attribute::whereHas('propertyTypes', function ($query) use ($propertyTypeId) {
                                                    $query->where('property_types.id', $propertyTypeId);
                                                })
                                                    ->pluck('name', 'id')
                                                    ->toArray();
                                            })
                                            ->label(__('attributes'))
                                            ->searchable(['name'])
                                            ->preload()
                                            ->disableOptionsWhenSelectedInSiblingRepeaterItems()
                                            ->required()
                                            ->columnSpan(1),
                                        TextInput::make('value')
                                            ->label(__('Value'))
                                            ->required(),
                                    ])


                            ])
                            ->defaultItems(0),

                        SpatieMediaLibraryFileUpload::make('attachment')
                            ->label(__('Attachment'))
                            ->image()
                            ->imageEditor()
                            ->collection(collection: "property_images")
                            ->imageEditorAspectRatios([
                                '16:9',
                                '4:3',
                                '1:1',
                            ])
                            ->panelLayout('grid')
                            ->reorderable()
                            ->multiple()
                            ->maxSize(5120)
                            ->downloadable()
                            ->openable(),
                        Repeater::make('vals')
                            ->label(__('val'))
                            ->collapsed(false)
                            ->collapsible()
                            ->relationship('vals')
                            ->schema([
                                TextInput::make('value')
                                    ->label(__('License number'))
                                    ->placeholder(__('License number'))
                                    ->required(),

                                Hidden::make('morphable_type')
                                    ->default(Property::class),

                                Hidden::make('morphable_id')
                                    ->default(function (Get $get) {
                                        return $get('../../id');
                                    }),

                                DatePicker::make('start_date')
                                    ->label(__('Start Date'))
                                    ->validationMessages([
                                        'required' => __('Start Date is required'),
                                    ])
                                    ->live()
                                    ->required(),

                                DatePicker::make('end_date')
                                    ->label(__('End Date'))
                                    ->validationMessages([
                                        'required' => __('End Date is required'),
                                    ])
                                    ->disabled(fn (Get $get): bool => ! $get('start_date'))
                                    ->minDate(fn (Get $get): string =>
                                    $get('start_date')
                                        ? \Carbon\Carbon::parse($get('start_date'))->addDay()->toDateString()
                                        : now()->toDateString()
                                    )
                                    ->required(),

                                Toggle::make('active')
                                    ->label(__('Active'))
                                    ->default(true)
                            ])
                            ->defaultItems(0)
                            ->maxItems(1)
                            ->columns(2)
                            ->mutateRelationshipDataBeforeCreateUsing(function (array $data): array {
                                return array_merge($data, [
                                    'company_id' => auth()->user()->company_id,
                                ]);
                            }),
                    ]),

                Grid::make(3)
                    ->label(__('Meters'))
                    ->relationship('meters')
                    ->schema([
                        Group::make([
                            TextInput::make('water_meter')
                                ->label(__('Water Meter'))
                                ->placeholder(__('Enter water meter number'))
                                ->nullable()
                                ->unique(
                                    table: 'property_meters',
                                    column: 'water_meter',
                                    ignorable: fn ($record) => $record
                                )
                                ->dehydrated()
                                ->prefixIcon('heroicon-o-beaker')
                                ->maxLength(50)
                                ->extraAttributes([
                                    'class' => 'bg-gray-50/50',
                                ])
                                ->validationMessages([
                                    'unique' => __('This water meter number is already in use'),
                                ])

                        ])->columnSpan(1),

                        Group::make([
                            TextInput::make('gas_meter')
                                ->label(__('Gas Meter'))
                                ->placeholder(__('Enter gas meter number'))
                                ->nullable()
                                ->unique(
                                    table: 'property_meters',
                                    column: 'gas_meter',
                                    ignorable: fn ($record) => $record
                                )
                                ->dehydrated()
                                ->prefixIcon('heroicon-o-fire')
                                ->maxLength(50)
                                ->extraAttributes([
                                    'class' => 'bg-gray-50/50',
                                ])
                                ->validationMessages([
                                    'unique' => __('This Gas meter number is already in use'),
                                ])
                        ])->columnSpan(1),

                        Group::make([
                            TextInput::make('electronic_meter')
                                ->label(__('Electronic Meter'))
                                ->placeholder(__('Enter electronic meter number'))
                                ->nullable()
                                ->unique(
                                    table: 'property_meters',
                                    column: 'electronic_meter',
                                    ignorable: fn ($record) => $record
                                )
                                ->dehydrated()
                                ->prefixIcon('heroicon-o-bolt')
                                ->maxLength(50)
                                ->extraAttributes([
                                    'class' => 'bg-gray-50/50',
                                ])
                                ->validationMessages([
                                    'unique' => __('This electronic meter number is already in use'),
                                ])
                        ])->columnSpan(1),
                    ])
                    ->columns(3)
                    ->extraAttributes([
                        'class' => 'mt-4 mb-4', // Add some vertical spacing
                    ]),

            ]);

    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label(__('id'))
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('number')
                    ->label(__('Unit Number'))
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('property.name')
                    ->label(__('Property'))
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('status')
                    ->label(__('Status'))
                    ->badge()
                    ->color(fn (PropertyStatus $state): string => $state->getColor())
                    ->formatStateUsing(fn (PropertyStatus $state): string => PropertyStatus::getLabel($state))
                    ->sortable(),
                Tables\Columns\TextColumn::make('property_type.name')
                    ->label(__('Property Type'))
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('usability.name')
                    ->label(__('Usability'))
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('price')
                    ->label(__('Price'))
                    ->searchable()
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('property_id')
                    ->label(__('Property'))
                    ->relationship('property', 'name',fn($query) => $query->where('parent_id', null))
                    ->searchable()
                    ->multiple()
                    ->preload(),
                Tables\Filters\SelectFilter::make('status')
                    ->label(__('Status'))
                    ->searchable()
                    ->multiple()
                    ->options(PropertyStatus::getFilterOptions()),
                Tables\Filters\SelectFilter::make('property_type_id')
                    ->label(__('Property Type'))
                    ->options(PropertyType::select('id', 'name')->where('property_type', 'unit')->pluck('name', 'id'))
                    ->native(false)
                    ->searchable()
                    ->multiple(),
                Tables\Filters\SelectFilter::make('usability_id')
                    ->label(__('Usability'))
                    ->options(Usability::select('id', 'name')->pluck('name', 'id'))
                    ->native(false)
                    ->searchable()
                    ->multiple(),
                Tables\Filters\SelectFilter::make('attributes')
                    ->label(__('Attribute'))
                    ->relationship('attributes', 'name')
                    ->native(false)
                    ->preload()
                    ->searchable()
                    ->multiple(),
                ValueRangeFilter::make('price')
                    ->label(__('Price')),
            ])
            ->actions([
                Tables\Actions\ViewAction::make()
                        ->modalWidth('7xl'),
                ActionGroup::make([
                    Tables\Actions\DeleteAction::make()
                        ->visible(fn (Unit $record): bool => $record->status === PropertyStatus::DRAFT || $record->status === PropertyStatus::ACTIVE),
                    Tables\Actions\EditAction::make()
                        ->modalWidth('7xl')
                        ->visible(fn (Unit $record): bool => $record->status === PropertyStatus::DRAFT || $record->status === PropertyStatus::ACTIVE),
                    Tables\Actions\Action::make('publish')
                        ->label(__('Publish'))
                        ->icon('heroicon-o-paper-airplane')
                        ->color('success')
                        ->requiresConfirmation()
                        ->modalHeading(__('Publish unit'))
                        ->modalDescription(__('Are you sure you want to publish this unit?'))
                        ->modalSubmitActionLabel(__('Yes, publish it'))
                        ->action(function (Unit $record): void {
                            Unit::query()
                            ->where(function ($query) use ($record) {
                                $query->where('id', $record->id)
                                    ->orWhere(function ($q) use ($record) {
                                        $q->where('id', $record->parent_id)
                                            ->where('status', PropertyStatus::DRAFT);
                                    });
                            })
                            ->update(['status' => PropertyStatus::ACTIVE]);

                            Notification::make()
                                ->title(__('Unit published successfully'))
                                ->success()
                                ->send();
                        })
                        ->visible(fn (Unit $record): bool => in_array($record->status, [PropertyStatus::DRAFT, PropertyStatus::IN_MAINTENANCE])),
                ])
                ->button()
                ->extraAttributes([
                    'class' => 'custom-action-btn',
                ])
                ->color('transparent')
                ->label(__('Commends')),

                ]);
    }


    public static function getPages(): array
    {
        return [
            'index' => UnitResource\Pages\ListUnits::route('/'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        $query = parent::getEloquentQuery();


        $query->withoutGlobalScopes([
                SoftDeletingScope::class,
            ])
            ->whereNotNull('parent_id');
        return $query;
    }

    public static function getWidgets(): array
    {
        return [
            UnitListOverview::class
        ];
    }

    public static function getNavigationLabel(): string
    {
        return __("untis");
    }

    public static function getNavigationGroup(): string
    {
        return __('Properties Management');
    }

    public static function getBreadcrumb() : string
    {
        return __('Unit');
    }
    public static function getModelLabel(): string
    {
        return __('Unit');
    }

    public static function getPluralModelLabel(): string
    {
        return __('units');
    }
}
